#!/usr/bin/env python
"""
Test script for confidence percentage calculation using cosine similarity
"""
import torch
import numpy as np
import math

def cosine_similarity(x1, x2, dim=1, eps=1e-8):
    """Calculate cosine similarity between two tensors"""
    # Normalize the tensors
    x1_norm = torch.nn.functional.normalize(x1, p=2, dim=dim, eps=eps)
    x2_norm = torch.nn.functional.normalize(x2, p=2, dim=dim, eps=eps)

    # Calculate cosine similarity
    similarity = torch.sum(x1_norm * x2_norm, dim=dim)
    return similarity

def similarity_to_confidence_percentage(similarity, scale=10.0):
    """
    Convert cosine similarity to confidence percentage using sigmoid transformation

    Args:
        similarity: Cosine similarity values (range: -1 to 1)
        scale: Scaling factor for sigmoid steepness (higher = steeper curve)

    Returns:
        Confidence percentage (range: 0 to 100)
    """
    # Shift similarity from [-1, 1] to [0, 2] range
    shifted_similarity = similarity + 1.0

    # Apply sigmoid transformation with scaling
    # Using a shifted and scaled sigmoid: 1 / (1 + exp(-scale * (x - 1)))
    sigmoid_input = scale * (shifted_similarity - 1.0)
    confidence = torch.sigmoid(sigmoid_input)

    # Convert to percentage (0-100)
    confidence_percentage = confidence * 100.0

    return confidence_percentage

def test_cosine_similarity():
    """Test cosine similarity calculation"""
    print("Testing cosine similarity calculation...")
    
    # Test case 1: Identical vectors (should give similarity = 1.0)
    vec1 = torch.tensor([[1.0, 0.0, 0.0]])
    vec2 = torch.tensor([[1.0, 0.0, 0.0]])
    similarity = cosine_similarity(vec1, vec2)
    print(f"Identical vectors similarity: {similarity.item():.4f} (expected: 1.0000)")
    
    # Test case 2: Orthogonal vectors (should give similarity = 0.0)
    vec1 = torch.tensor([[1.0, 0.0, 0.0]])
    vec2 = torch.tensor([[0.0, 1.0, 0.0]])
    similarity = cosine_similarity(vec1, vec2)
    print(f"Orthogonal vectors similarity: {similarity.item():.4f} (expected: 0.0000)")
    
    # Test case 3: Opposite vectors (should give similarity = -1.0)
    vec1 = torch.tensor([[1.0, 0.0, 0.0]])
    vec2 = torch.tensor([[-1.0, 0.0, 0.0]])
    similarity = cosine_similarity(vec1, vec2)
    print(f"Opposite vectors similarity: {similarity.item():.4f} (expected: -1.0000)")
    
    # Test case 4: Similar but not identical vectors
    vec1 = torch.tensor([[1.0, 1.0, 0.0]])
    vec2 = torch.tensor([[1.0, 0.8, 0.0]])
    similarity = cosine_similarity(vec1, vec2)
    print(f"Similar vectors similarity: {similarity.item():.4f} (expected: ~0.9487)")

def test_confidence_percentage():
    """Test confidence percentage conversion"""
    print("\nTesting confidence percentage conversion...")
    
    # Test various similarity values
    test_similarities = torch.tensor([1.0, 0.9, 0.8, 0.7, 0.5, 0.0, -0.5, -1.0])
    
    for sim in test_similarities:
        confidence = similarity_to_confidence_percentage(sim)
        print(f"Similarity: {sim.item():.1f} -> Confidence: {confidence.item():.1f}%")

def test_batch_processing():
    """Test batch processing with multiple embeddings"""
    print("\nTesting batch processing...")
    
    # Create batch of source embeddings
    source_embs = torch.tensor([
        [1.0, 0.0, 0.0],  # Will match target 0
        [0.0, 1.0, 0.0],  # Will match target 1
        [0.5, 0.5, 0.0],  # Will partially match both
    ])
    
    # Create target embeddings
    target_embs = torch.tensor([
        [1.0, 0.0, 0.0],  # Target 0
        [0.0, 1.0, 0.0],  # Target 1
    ]).T  # Transpose for matrix multiplication
    
    # Normalize embeddings
    source_embs_norm = torch.nn.functional.normalize(source_embs, p=2, dim=1)
    target_embs_norm = torch.nn.functional.normalize(target_embs, p=2, dim=0)
    
    # Calculate similarities
    similarities = torch.mm(source_embs_norm, target_embs_norm)
    
    # Find best matches
    maximum, max_idx = torch.max(similarities, dim=1)
    
    # Convert to confidence percentages
    confidence_percentages = similarity_to_confidence_percentage(maximum)
    
    print("Batch processing results:")
    for i, (idx, conf) in enumerate(zip(max_idx, confidence_percentages)):
        print(f"Source {i}: Best match = Target {idx.item()}, Confidence = {conf.item():.1f}%")

if __name__ == "__main__":
    print("=" * 60)
    print("CONFIDENCE PERCENTAGE CALCULATION TEST")
    print("=" * 60)
    
    test_cosine_similarity()
    test_confidence_percentage()
    test_batch_processing()
    
    print("\n" + "=" * 60)
    print("TEST COMPLETED")
    print("=" * 60)
