from django import template

register = template.Library()

@register.filter
def to_percentage(value):
    """Convert confidence value (0-1) to percentage (0-100)"""
    try:
        return float(value) * 100
    except (ValueError, TypeError):
        return 0

@register.filter
def format_confidence(value):
    """Format confidence value as percentage with proper rounding"""
    try:
        percentage = float(value) * 100
        return f"{percentage:.0f}%"
    except (ValueError, TypeError):
        return "0%"
