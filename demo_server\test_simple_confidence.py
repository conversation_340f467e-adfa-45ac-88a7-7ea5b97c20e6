#!/usr/bin/env python
"""
Test script for simple linear confidence calculation
"""
import torch

def test_linear_confidence():
    """Test linear confidence calculation: confidence = 1 - (distance / max_distance)"""
    print("Testing linear confidence calculation...")
    
    threshold = 1.11  # Max distance (threshold)
    
    # Test different distance values
    test_distances = torch.tensor([0.0, 0.2, 0.5, 0.8, 1.0, 1.11, 1.5])
    
    print(f"Threshold (max_distance): {threshold}")
    print("Distance -> Confidence")
    print("-" * 25)
    
    for distance in test_distances:
        # Linear confidence calculation
        confidence = 1.0 - (distance / threshold)
        confidence = torch.clamp(confidence, 0.0, 1.0)  # Ensure 0-1 range
        
        confidence_percentage = confidence * 100
        
        print(f"{distance:.2f}     -> {confidence:.3f} ({confidence_percentage:.0f}%)")

if __name__ == "__main__":
    print("=" * 50)
    print("SIMPLE LINEAR CONFIDENCE TEST")
    print("=" * 50)
    
    test_linear_confidence()
    
    print("\n" + "=" * 50)
    print("TEST COMPLETED")
    print("=" * 50)
